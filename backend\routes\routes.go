package routes

import (
	"file-manager/handlers"
	"file-manager/middleware"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置路由
func SetupRoutes(r *gin.Engine) {
	// 创建处理器实例
	authHandler := handlers.NewAuthHandler()
	fileHandler := handlers.NewFileHandler()

	// API路由组
	api := r.Group("/api")
	{
		// 认证相关路由（无需认证）
		auth := api.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", authHandler.Logout)
		}

		// 需要认证的路由
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware())
		{
			// 用户信息
			protected.GET("/profile", authHandler.GetProfile)
			protected.PUT("/password", authHandler.UpdatePassword)

			// 文件管理
			files := protected.Group("/files")
			{
				files.POST("/upload", fileHandler.UploadFile)
				files.GET("/list", fileHandler.GetFileList)
				files.GET("/my", fileHandler.GetUserFiles)
				files.GET("/:id/download", fileHandler.DownloadFile)
				files.DELETE("/:id", fileHandler.DeleteFile)
			}
		}
	}

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status": "ok",
			"message": "File Manager API is running",
		})
	})
}
