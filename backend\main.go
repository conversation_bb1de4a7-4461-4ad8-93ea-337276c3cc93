package main

import (
    "log"
    "github.com/gin-gonic/gin"
    "file-manager/config"
    "file-manager/database"
    "file-manager/routes"
    "file-manager/middleware"
)

func main() {
    // 初始化配置
    config.Init()
    
    // 初始化数据库
    database.Init()
    
    // 创建Gin引擎
    r := gin.Default()
    
    // 中间件
    r.Use(middleware.CORS())
    r.Use(gin.Logger())
    r.Use(gin.Recovery())
    
    // 静态文件服务
    r.Static("/uploads", "./uploads")
    
    // 路由
    routes.SetupRoutes(r)
    
    log.Println("Server starting on :8080")
    r.Run(":8080")
}