#include <iostream>
#include <string>
#include <curl/curl.h>
#include <json/json.h>
#include <fstream>

class FileClient {
private:
    std::string baseUrl;
    std::string token;
    
    static size_t WriteCallback(void* contents, size_t size, size_t nmemb, std::string* data) {
        data->append((char*)contents, size * nmemb);
        return size * nmemb;
    }

public:
    FileClient(const std::string& url) : baseUrl(url) {
        curl_global_init(CURL_GLOBAL_DEFAULT);
    }
    
    ~FileClient() {
        curl_global_cleanup();
    }
    
    bool login(const std::string& username, const std::string& password) {
        CURL* curl = curl_easy_init();
        if (!curl) return false;
        
        std::string response;
        std::string url = baseUrl + "/api/auth/login";
        
        Json::Value loginData;
        loginData["username"] = username;
        loginData["password"] = password;
        
        Json::StreamWriterBuilder builder;
        std::string jsonString = Json::writeString(builder, loginData);
        
        struct curl_slist* headers = nullptr;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, jsonString.c_str());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        
        CURLcode res = curl_easy_perform(curl);
        
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
        if (res == CURLE_OK) {
            Json::Value root;
            Json::Reader reader;
            if (reader.parse(response, root)) {
                token = root["token"].asString();
                return !token.empty();
            }
        }
        return false;
    }
    
    bool uploadFile(const std::string& filePath) {
        CURL* curl = curl_easy_init();
        if (!curl) return false;
        
        std::string response;
        std::string url = baseUrl + "/api/files/upload";
        
        curl_mime* form = curl_mime_init(curl);
        curl_mimepart* field = curl_mime_addpart(form);
        
        curl_mime_name(field, "file");
        curl_mime_filedata(field, filePath.c_str());
        
        struct curl_slist* headers = nullptr;
        std::string authHeader = "Authorization: Bearer " + token;
        headers = curl_slist_append(headers, authHeader.c_str());
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_MIMEPOST, form);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        
        CURLcode res = curl_easy_perform(curl);
        
        curl_slist_free_all(headers);
        curl_mime_free(form);
        curl_easy_cleanup(curl);
        
        return res == CURLE_OK;
    }
    
    std::vector<FileInfo> getFileList() {
        CURL* curl = curl_easy_init();
        std::vector<FileInfo> files;
        
        if (!curl) return files;
        
        std::string response;
        std::string url = baseUrl + "/api/files";
        
        struct curl_slist* headers = nullptr;
        std::string authHeader = "Authorization: Bearer " + token;
        headers = curl_slist_append(headers, authHeader.c_str());
        
        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response);
        
        CURLcode res = curl_easy_perform(curl);
        
        if (res == CURLE_OK) {
            Json::Value root;
            Json::Reader reader;
            if (reader.parse(response, root)) {
                const Json::Value& fileArray = root["files"];
                for (const auto& file : fileArray) {
                    FileInfo info;
                    info.id = file["id"].asInt();
                    info.filename = file["original_name"].asString();
                    info.size = file["file_size"].asInt64();
                    files.push_back(info);
                }
            }
        }
        
        curl_slist_free_all(headers);
        curl_easy_cleanup(curl);
        
        return files;
    }
};

// 使用示例
int main() {
    FileClient client("http://localhost:8080");
    
    if (client.login("admin", "password")) {
        std::cout << "登录成功" << std::endl;
        
        // 上传文件
        if (client.uploadFile("./test.txt")) {
            std::cout << "文件上传成功" << std::endl;
        }
        
        // 获取文件列表
        auto files = client.getFileList();
        for (const auto& file : files) {
            std::cout << "文件: " << file.filename << ", 大小: " << file.size << std::endl;
        }
    }
    
    return 0;
}