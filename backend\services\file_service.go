package services

import (
	"errors"
	"file-manager/database"
	"file-manager/models"
	"file-manager/utils"
	"mime/multipart"
	"path/filepath"

	"gorm.io/gorm"
)

type FileService struct {
	db *gorm.DB
}

func NewFileService() *FileService {
	return &FileService{
		db: database.DB,
	}
}

// UploadFile 上传文件
func (s *FileService) UploadFile(fileHeader *multipart.FileHeader, userID uint) (*models.File, error) {
	// 1. 打开上传的文件
	file, err := fileHeader.Open()
	if err != nil {
		return nil, errors.New("无法打开上传文件")
	}
	defer file.Close()

	// 2. 验证文件类型
	mimeType := fileHeader.Header.Get("Content-Type")
	if !utils.IsValidFileType(mimeType) {
		return nil, errors.New("不支持的文件类型")
	}

	// 3. 验证文件大小 (限制为50MB)
	const maxFileSize = 50 * 1024 * 1024 // 50MB
	if fileHeader.Size > maxFileSize {
		return nil, errors.New("文件大小超过限制")
	}

	// 4. 生成唯一文件名
	filename := utils.GenerateUniqueFilename(fileHeader.Filename)
	filePath := filepath.Join("uploads", filename)

	// 5. 计算MD5哈希
	md5Hash, err := utils.CalculateMD5(file)
	if err != nil {
		return nil, errors.New("计算文件哈希失败")
	}

	// 6. 保存文件到磁盘
	if err := utils.SaveUploadedFile(file, filename); err != nil {
		return nil, errors.New("保存文件失败")
	}

	// 7. 保存文件信息到数据库
	fileModel := models.File{
		Filename:     filename,
		OriginalName: fileHeader.Filename,
		FilePath:     filePath,
		FileSize:     fileHeader.Size,
		MimeType:     mimeType,
		MD5Hash:      md5Hash,
		UploadedBy:   userID,
	}

	if err := s.db.Create(&fileModel).Error; err != nil {
		return nil, errors.New("保存文件信息失败")
	}

	return &fileModel, nil
}

// GetFileList 获取文件列表
func (s *FileService) GetFileList(page, limit int, search string) (*models.FileListResponse, error) {
	var files []models.File
	var total int64

	// 构建查询
	query := s.db.Model(&models.File{}).Where("is_deleted = ?", false)

	// 搜索条件
	if search != "" {
		query = query.Where("original_name LIKE ? OR filename LIKE ?", "%"+search+"%", "%"+search+"%")
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, errors.New("查询文件总数失败")
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Preload("Admin").Offset(offset).Limit(limit).Order("upload_time DESC").Find(&files).Error; err != nil {
		return nil, errors.New("查询文件列表失败")
	}

	return &models.FileListResponse{
		Files: files,
		Total: total,
		Page:  page,
		Limit: limit,
	}, nil
}

// GetFileByID 根据ID获取文件信息
func (s *FileService) GetFileByID(fileID uint) (*models.File, error) {
	var file models.File
	err := s.db.Where("id = ? AND is_deleted = ?", fileID, false).First(&file).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}
	return &file, nil
}

// DeleteFile 删除文件（软删除）
func (s *FileService) DeleteFile(fileID, userID uint) error {
	var file models.File
	err := s.db.Where("id = ? AND is_deleted = ?", fileID, false).First(&file).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("文件不存在")
		}
		return err
	}

	// 验证文件所有权（可选，根据需求决定是否需要）
	// if file.UploadedBy != userID {
	//     return errors.New("无权限删除此文件")
	// }

	// 软删除
	if err := s.db.Model(&file).Update("is_deleted", true).Error; err != nil {
		return errors.New("删除文件失败")
	}

	return nil
}

// GetFilesByUser 获取用户上传的文件列表
func (s *FileService) GetFilesByUser(userID uint, page, limit int) (*models.FileListResponse, error) {
	var files []models.File
	var total int64

	query := s.db.Model(&models.File{}).Where("uploaded_by = ? AND is_deleted = ?", userID, false)

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, errors.New("查询用户文件总数失败")
	}

	// 分页查询
	offset := (page - 1) * limit
	if err := query.Offset(offset).Limit(limit).Order("upload_time DESC").Find(&files).Error; err != nil {
		return nil, errors.New("查询用户文件列表失败")
	}

	return &models.FileListResponse{
		Files: files,
		Total: total,
		Page:  page,
		Limit: limit,
	}, nil
}
