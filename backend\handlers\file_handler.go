package handlers

import (
    "crypto/md5"
    "fmt"
    "io"
    "net/http"
    "os"
    "path/filepath"
    "strconv"
    "time"
    
    "github.com/gin-gonic/gin"
    "file-manager/database"
    "file-manager/models"
)

func UploadFile(c *gin.Context) {
    file, header, err := c.Request.FormFile("file")
    if err != nil {
        c.JSON(http.StatusBadRequest, gin.H{"error": "No file uploaded"})
        return
    }
    defer file.Close()

    // 生成唯一文件名
    filename := fmt.Sprintf("%d_%s", time.Now().Unix(), header.Filename)
    filepath := filepath.Join("uploads", filename)
    
    // 保存文件
    out, err := os.Create(filepath)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
        return
    }
    defer out.Close()
    
    // 计算MD5
    hash := md5.New()
    size, err := io.Copy(io.MultiWriter(out, hash), file)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
        return
    }
    
    md5Hash := fmt.Sprintf("%x", hash.Sum(nil))
    
    // 保存到数据库
    _, err = database.DB.Exec(`
        INSERT INTO files (filename, original_name, file_path, file_size, mime_type, md5_hash, uploaded_by)
        VALUES (?, ?, ?, ?, ?, ?, ?)`,
        filename, header.Filename, filepath, size, header.Header.Get("Content-Type"), md5Hash, 1)
    
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file info"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "File uploaded successfully", "filename": filename})
}

func GetFileList(c *gin.Context) {
    rows, err := database.DB.Query(`
        SELECT id, filename, original_name, file_size, mime_type, upload_time 
        FROM files WHERE is_deleted = FALSE ORDER BY upload_time DESC`)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to get file list"})
        return
    }
    defer rows.Close()
    
    var files []models.File
    for rows.Next() {
        var file models.File
        err := rows.Scan(&file.ID, &file.Filename, &file.OriginalName, 
                        &file.FileSize, &file.MimeType, &file.UploadTime)
        if err != nil {
            continue
        }
        files = append(files, file)
    }
    
    c.JSON(http.StatusOK, gin.H{"files": files})
}

func DownloadFile(c *gin.Context) {
    fileID := c.Param("id")
    
    var file models.File
    err := database.DB.QueryRow(`
        SELECT filename, original_name, file_path, mime_type 
        FROM files WHERE id = ? AND is_deleted = FALSE`, fileID).
        Scan(&file.Filename, &file.OriginalName, &file.FilePath, &file.MimeType)
    
    if err != nil {
        c.JSON(http.StatusNotFound, gin.H{"error": "File not found"})
        return
    }
    
    c.Header("Content-Disposition", "attachment; filename="+file.OriginalName)
    c.Header("Content-Type", file.MimeType)
    c.File(file.FilePath)
}

func DeleteFile(c *gin.Context) {
    fileID := c.Param("id")
    
    _, err := database.DB.Exec("UPDATE files SET is_deleted = TRUE WHERE id = ?", fileID)
    if err != nil {
        c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to delete file"})
        return
    }
    
    c.JSON(http.StatusOK, gin.H{"message": "File deleted successfully"})
}