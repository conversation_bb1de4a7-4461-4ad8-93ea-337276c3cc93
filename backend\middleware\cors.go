func CORS() gin.HandlerFunc {
    return func(c *gin.Context) {
        c.<PERSON>("Access-Control-Allow-Origin", "*")
        c.<PERSON>("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
        c.<PERSON>("Access-Control-Allow-Headers", "Content-Type, Authorization")
        
        if c.Request.Method == "OPTIONS" {
            c.AbortWithStatus(204)
            return
        }
        
        c.Next()
    }
}