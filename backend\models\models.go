package models

import (
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type Admin struct {
	ID           uint      `json:"id" gorm:"primaryKey"`
	Username     string    `json:"username" gorm:"uniqueIndex;not null;size:50"`
	PasswordHash string    `json:"-" gorm:"not null;size:255"`
	Email        string    `json:"email" gorm:"size:100"`
	CreatedAt    time.Time `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updated_at" gorm:"autoUpdateTime"`

	// 关联关系
	Files []File `json:"files,omitempty" gorm:"foreignKey:UploadedBy"`
}

// SetPassword 设置密码哈希
func (a *Admin) SetPassword(password string) error {
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	a.PasswordHash = string(hashedPassword)
	return nil
}

// CheckPassword 验证密码
func (a *Admin) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(a.PasswordHash), []byte(password))
	return err == nil
}

type File struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	Filename     string         `json:"filename" gorm:"not null;size:255"`
	OriginalName string         `json:"original_name" gorm:"not null;size:255"`
	FilePath     string         `json:"file_path" gorm:"not null;size:500"`
	FileSize     int64          `json:"file_size" gorm:"not null"`
	MimeType     string         `json:"mime_type" gorm:"size:100"`
	MD5Hash      string         `json:"md5_hash" gorm:"size:32"`
	UploadTime   time.Time      `json:"upload_time" gorm:"autoCreateTime"`
	UploadedBy   uint           `json:"uploaded_by" gorm:"not null"`
	IsDeleted    bool           `json:"is_deleted" gorm:"default:false"`
	DeletedAt    gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// 关联关系
	Admin Admin `json:"admin,omitempty" gorm:"foreignKey:UploadedBy"`
}

// 请求和响应结构体
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginResponse struct {
	Token string `json:"token"`
	Admin Admin  `json:"admin"`
}

type FileListRequest struct {
	Page   int    `json:"page" form:"page"`
	Limit  int    `json:"limit" form:"limit"`
	Search string `json:"search" form:"search"`
}

type FileListResponse struct {
	Files []File `json:"files"`
	Total int64  `json:"total"`
	Page  int    `json:"page"`
	Limit int    `json:"limit"`
}

type APIResponse struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp time.Time   `json:"timestamp"`
}
