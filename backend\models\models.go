package models

import "time"

type Admin struct {
    ID           int       `json:"id" db:"id"`
    Username     string    `json:"username" db:"username"`
    PasswordHash string    `json:"-" db:"password_hash"`
    Email        string    `json:"email" db:"email"`
    CreatedAt    time.Time `json:"created_at" db:"created_at"`
}

type File struct {
    ID           int       `json:"id" db:"id"`
    Filename     string    `json:"filename" db:"filename"`
    OriginalName string    `json:"original_name" db:"original_name"`
    FilePath     string    `json:"file_path" db:"file_path"`
    FileSize     int64     `json:"file_size" db:"file_size"`
    MimeType     string    `json:"mime_type" db:"mime_type"`
    MD5Hash      string    `json:"md5_hash" db:"md5_hash"`
    UploadTime   time.Time `json:"upload_time" db:"upload_time"`
    UploadedBy   int       `json:"uploaded_by" db:"uploaded_by"`
}

type LoginRequest struct {
    Username string `json:"username" binding:"required"`
    Password string `json:"password" binding:"required"`
}