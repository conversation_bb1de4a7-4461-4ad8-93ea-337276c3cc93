package utils

import (
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// GenerateUniqueFilename 生成唯一文件名
func GenerateUniqueFilename(originalName string) string {
	ext := filepath.Ext(originalName)
	name := strings.TrimSuffix(originalName, ext)
	timestamp := time.Now().Unix()
	return fmt.Sprintf("%s_%d%s", name, timestamp, ext)
}

// CalculateMD5 计算文件MD5哈希
func CalculateMD5(file multipart.File) (string, error) {
	hash := md5.New()
	_, err := io.Copy(hash, file)
	if err != nil {
		return "", err
	}
	
	// 重置文件指针到开始位置
	file.Seek(0, 0)
	
	return fmt.Sprintf("%x", hash.Sum(nil)), nil
}

// SaveUploadedFile 保存上传的文件
func SaveUploadedFile(file multipart.File, filename string) error {
	// 确保uploads目录存在
	uploadsDir := "uploads"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		return err
	}
	
	// 创建目标文件
	dst, err := os.Create(filepath.Join(uploadsDir, filename))
	if err != nil {
		return err
	}
	defer dst.Close()
	
	// 复制文件内容
	_, err = io.Copy(dst, file)
	return err
}

// GetFileSize 获取文件大小
func GetFileSize(file multipart.File) (int64, error) {
	// 移动到文件末尾获取大小
	size, err := file.Seek(0, 2)
	if err != nil {
		return 0, err
	}
	
	// 重置文件指针到开始位置
	file.Seek(0, 0)
	
	return size, nil
}

// IsValidFileType 验证文件类型
func IsValidFileType(mimeType string) bool {
	allowedTypes := []string{
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/webp",
		"text/plain",
		"text/csv",
		"application/pdf",
		"application/msword",
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document",
		"application/vnd.ms-excel",
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
		"application/zip",
		"application/x-rar-compressed",
	}
	
	for _, allowedType := range allowedTypes {
		if mimeType == allowedType {
			return true
		}
	}
	return false
}

// FormatFileSize 格式化文件大小显示
func FormatFileSize(size int64) string {
	const unit = 1024
	if size < unit {
		return fmt.Sprintf("%d B", size)
	}
	div, exp := int64(unit), 0
	for n := size / unit; n >= unit; n /= unit {
		div *= unit
		exp++
	}
	return fmt.Sprintf("%.1f %cB", float64(size)/float64(div), "KMGTPE"[exp])
}
