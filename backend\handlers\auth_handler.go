package handlers

import (
	"file-manager/models"
	"file-manager/services"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

type AuthHandler struct {
	authService *services.AuthService
}

func NewAuthHandler() *AuthHandler {
	return &AuthHandler{
		authService: services.NewAuthService(),
	}
}

// Login 管理员登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req models.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Timestamp: time.Now(),
		})
		return
	}

	// 执行登录
	response, err := h.authService.Login(req.Username, req.Password)
	if err != nil {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Code:      401,
			Message:   err.Error(),
			Timestamp: time.Now(),
		})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, models.APIResponse{
		Code:      200,
		Message:   "登录成功",
		Data:      response,
		Timestamp: time.Now(),
	})
}

// GetProfile 获取当前用户信息
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Code:      401,
			Message:   "未授权访问",
			Timestamp: time.Now(),
		})
		return
	}

	admin, err := h.authService.GetAdminByID(userID.(uint))
	if err != nil {
		c.JSON(http.StatusNotFound, models.APIResponse{
			Code:      404,
			Message:   err.Error(),
			Timestamp: time.Now(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:      200,
		Message:   "获取用户信息成功",
		Data:      admin,
		Timestamp: time.Now(),
	})
}

// UpdatePassword 更新密码
func (h *AuthHandler) UpdatePassword(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, models.APIResponse{
			Code:      401,
			Message:   "未授权访问",
			Timestamp: time.Now(),
		})
		return
	}

	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=6"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:      400,
			Message:   "请求参数错误",
			Timestamp: time.Now(),
		})
		return
	}

	err := h.authService.UpdatePassword(userID.(uint), req.OldPassword, req.NewPassword)
	if err != nil {
		c.JSON(http.StatusBadRequest, models.APIResponse{
			Code:      400,
			Message:   err.Error(),
			Timestamp: time.Now(),
		})
		return
	}

	c.JSON(http.StatusOK, models.APIResponse{
		Code:      200,
		Message:   "密码更新成功",
		Timestamp: time.Now(),
	})
}

// Logout 登出（客户端处理，服务端可以记录日志）
func (h *AuthHandler) Logout(c *gin.Context) {
	c.JSON(http.StatusOK, models.APIResponse{
		Code:      200,
		Message:   "登出成功",
		Timestamp: time.Now(),
	})
}
