package middleware

import (
	"file-manager/models"
	"file-manager/utils"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.<PERSON>("Authorization")
		if token == "" {
			c.JSON(http.StatusUnauthorized, models.APIResponse{
				Code:      401,
				Message:   "未提供认证令牌",
				Timestamp: time.Now(),
			})
			c.Abort()
			return
		}

		// 验证JWT Token
		claims, err := utils.ValidateJWT(token)
		if err != nil {
			c.J<PERSON>(http.StatusUnauthorized, models.APIResponse{
				Code:      401,
				Message:   "无效的认证令牌",
				Timestamp: time.Now(),
			})
			c.Abort()
			return
		}

		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Next()
	}
}
