func AuthMiddleware() gin.HandlerFunc {
    return func(c *gin.Context) {
        token := c<PERSON>("Authorization")
        if token == "" {
            c.JSON(401, gin.H{"error": "未提供认证令牌"})
            c.Abort()
            return
        }
        
        // 验证JWT Token
        claims, err := utils.ValidateJWT(token)
        if err != nil {
            c.JSON(401, gin.H{"error": "无效的认证令牌"})
            c.Abort()
            return
        }
        
        c.<PERSON>("user_id", claims.UserID)
        c.Next()
    }
}