package services

import (
	"errors"
	"file-manager/database"
	"file-manager/models"
	"file-manager/utils"

	"gorm.io/gorm"
)

type AuthService struct {
	db *gorm.DB
}

func NewAuthService() *AuthService {
	return &AuthService{
		db: database.DB,
	}
}

// Login 管理员登录
func (s *AuthService) Login(username, password string) (*models.LoginResponse, error) {
	var admin models.Admin
	
	// 查找用户
	err := s.db.Where("username = ?", username).First(&admin).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名或密码错误")
		}
		return nil, err
	}
	
	// 验证密码
	if !admin.CheckPassword(password) {
		return nil, errors.New("用户名或密码错误")
	}
	
	// 生成JWT令牌
	token, err := utils.GenerateJWT(admin.ID, admin.Username)
	if err != nil {
		return nil, errors.New("生成令牌失败")
	}
	
	return &models.LoginResponse{
		Token: token,
		Admin: admin,
	}, nil
}

// GetAdminByID 根据ID获取管理员信息
func (s *AuthService) GetAdminByID(id uint) (*models.Admin, error) {
	var admin models.Admin
	err := s.db.First(&admin, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("管理员不存在")
		}
		return nil, err
	}
	return &admin, nil
}

// CreateAdmin 创建新管理员
func (s *AuthService) CreateAdmin(username, email, password string) (*models.Admin, error) {
	// 检查用户名是否已存在
	var count int64
	s.db.Model(&models.Admin{}).Where("username = ?", username).Count(&count)
	if count > 0 {
		return nil, errors.New("用户名已存在")
	}
	
	admin := models.Admin{
		Username: username,
		Email:    email,
	}
	
	// 设置密码哈希
	if err := admin.SetPassword(password); err != nil {
		return nil, errors.New("密码加密失败")
	}
	
	// 保存到数据库
	if err := s.db.Create(&admin).Error; err != nil {
		return nil, errors.New("创建管理员失败")
	}
	
	return &admin, nil
}

// UpdatePassword 更新密码
func (s *AuthService) UpdatePassword(adminID uint, oldPassword, newPassword string) error {
	var admin models.Admin
	err := s.db.First(&admin, adminID).Error
	if err != nil {
		return errors.New("管理员不存在")
	}
	
	// 验证旧密码
	if !admin.CheckPassword(oldPassword) {
		return errors.New("原密码错误")
	}
	
	// 设置新密码
	if err := admin.SetPassword(newPassword); err != nil {
		return errors.New("密码加密失败")
	}
	
	// 更新数据库
	if err := s.db.Save(&admin).Error; err != nil {
		return errors.New("更新密码失败")
	}
	
	return nil
}

// ValidateToken 验证令牌并返回管理员信息
func (s *AuthService) ValidateToken(tokenString string) (*models.Admin, error) {
	claims, err := utils.ValidateJWT(tokenString)
	if err != nil {
		return nil, errors.New("无效的令牌")
	}
	
	return s.GetAdminByID(claims.UserID)
}
