class FileClient {
private:
    std::string baseUrl;
    std::string token;
    CURL* curl;
    
    // HTTP请求封装
    std::string makeRequest(const std::string& url, 
                           const std::string& method,
                           const std::string& data = "",
                           const std::vector<std::string>& headers = {});
    
    // 响应解析
    Json::Value parseResponse(const std::string& response);
    
public:
    FileClient(const std::string& baseUrl);
    ~FileClient();
    
    // 认证相关
    bool login(const std::string& username, const std::string& password);
    void logout();
    
    // 文件操作
    bool uploadFile(const std::string& filePath);
    std::vector<FileInfo> getFileList(int page = 1, int limit = 20);
    bool downloadFile(int fileId, const std::string& savePath);
    bool deleteFile(int fileId);
    
    // 工具方法
    bool isAuthenticated() const;
    std::string getLastError() const;
};