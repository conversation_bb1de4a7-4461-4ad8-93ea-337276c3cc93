<template>
  <div class="file-manager">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>文件管理</span>
          <el-upload
            :action="uploadUrl"
            :headers="headers"
            :on-success="handleUploadSuccess"
            :show-file-list="false"
          >
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </div>
      </template>
      
      <el-table :data="fileList" style="width: 100%">
        <el-table-column prop="original_name" label="文件名" />
        <el-table-column prop="file_size" label="大小" :formatter="formatFileSize" />
        <el-table-column prop="upload_time" label="上传时间" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button size="small" @click="previewFile(scope.row)">预览</el-button>
            <el-button size="small" @click="downloadFile(scope.row)">下载</el-button>
            <el-button size="small" type="danger" @click="deleteFile(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    
    <!-- 文件预览对话框 -->
    <el-dialog v-model="previewVisible" title="文件预览" width="80%">
      <div v-if="previewFile.mime_type?.startsWith('image/')">
        <img :src="getFileUrl(previewFile)" style="max-width: 100%" />
      </div>
      <div v-else>
        <p>该文件类型不支持预览</p>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { fileApi } from '@/api/file'

interface FileItem {
  id: number
  filename: string
  original_name: string
  file_size: number
  mime_type: string
  upload_time: string
}

const fileList = ref<FileItem[]>([])
const previewVisible = ref(false)
const previewFile = ref<FileItem>({} as FileItem)

const uploadUrl = ref(`${import.meta.env.VITE_API_BASE_URL}/api/files/upload`)
const headers = ref({
  'Authorization': `Bearer ${localStorage.getItem('token')}`
})

const loadFileList = async () => {
  try {
    const response = await fileApi.getFileList()
    fileList.value = response.data.files
  } catch (error) {
    ElMessage.error('获取文件列表失败')
  }
}

const handleUploadSuccess = () => {
  ElMessage.success('文件上传成功')
  loadFileList()
}

const downloadFile = (file: FileItem) => {
  window.open(`${import.meta.env.VITE_API_BASE_URL}/api/files/download/${file.id}`)
}

const deleteFile = async (file: FileItem) => {
  try {
    await fileApi.deleteFile(file.id)
    ElMessage.success('文件删除成功')
    loadFileList()
  } catch (error) {
    ElMessage.error('文件删除失败')
  }
}

const formatFileSize = (row: any, column: any, cellValue: number) => {
  const sizes = ['B', 'KB', 'MB', 'GB']
  let i = 0
  while (cellValue >= 1024 && i < sizes.length - 1) {
    cellValue /= 1024
    i++
  }
  return `${cellValue.toFixed(2)} ${sizes[i]}`
}

onMounted(() => {
  loadFileList()
})
</script>