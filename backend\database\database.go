package database

import (
	"file-manager/models"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

var DB *gorm.DB

func Init() {
	var err error

	// 配置GORM日志
	gormLogger := logger.Default.LogMode(logger.Info)

	// 连接SQLite数据库，使用纯Go实现（不使用CGO）
	DB, err = gorm.Open(sqlite.Open("filemanager.db"), &gorm.Config{
		Logger: gormLogger,
	})

	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 自动迁移数据库表
	err = DB.AutoMigrate(&models.Admin{}, &models.File{})
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 创建默认管理员账户
	createDefaultAdmin()

	log.Println("Database connected and migrated successfully")
}

func createDefaultAdmin() {
	var count int64
	DB.Model(&models.Admin{}).Count(&count)

	if count == 0 {
		// 创建默认管理员账户
		admin := models.Admin{
			Username: "admin",
			Email:    "<EMAIL>",
		}
		// 密码将在认证服务中设置
		if err := admin.SetPassword("admin123"); err != nil {
			log.Printf("Failed to set default admin password: %v", err)
			return
		}

		if err := DB.Create(&admin).Error; err != nil {
			log.Printf("Failed to create default admin: %v", err)
		} else {
			log.Println("Default admin created: username=admin, password=admin123")
		}
	}
}
