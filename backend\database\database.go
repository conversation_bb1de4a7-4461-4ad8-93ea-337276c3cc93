package database

import (
    "database/sql"
    "log"
    _ "github.com/mattn/go-sqlite3"
)

var DB *sql.DB

func Init() {
    var err error
    DB, err = sql.Open("sqlite3", "./filemanager.db")
    if err != nil {
        log.Fatal("Failed to connect to database:", err)
    }
    
    if err = DB.Ping(); err != nil {
        log.Fatal("Failed to ping database:", err)
    }
    
    createTables()
    log.Println("Database connected successfully")
}

func createTables() {
    // 执行schema.sql中的建表语句
    // ... 省略具体实现
}