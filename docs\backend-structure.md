backend/
├── main.go              # 程序入口
├── config/              # 配置管理
│   └── config.go
├── database/            # 数据库连接
│   └── database.go
├── models/              # 数据模型
│   ├── admin.go
│   └── file.go
├── handlers/            # 请求处理器
│   ├── auth_handler.go
│   └── file_handler.go
├── middleware/          # 中间件
│   ├── auth.go
│   ├── cors.go
│   └── logger.go
├── routes/              # 路由配置
│   └── routes.go
├── services/            # 业务逻辑
│   ├── auth_service.go
│   └── file_service.go
├── utils/               # 工具函数
│   ├── jwt.go
│   ├── hash.go
│   └── file.go
└── uploads/             # 文件存储目录