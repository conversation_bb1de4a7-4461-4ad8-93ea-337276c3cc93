package config

import (
	"log"
	"os"
)

// Config 应用配置
type Config struct {
	Port         string
	DatabasePath string
	JWTSecret    string
	UploadDir    string
}

var AppConfig *Config

// Init 初始化配置
func Init() {
	AppConfig = &Config{
		Port:         getEnv("PORT", "8081"),
		DatabasePath: getEnv("DATABASE_PATH", "filemanager.db"),
		JWTSecret:    getEnv("JWT_SECRET", "your-secret-key-here"),
		UploadDir:    getEnv("UPLOAD_DIR", "uploads"),
	}

	// 创建上传目录
	if err := os.MkdirAll(AppConfig.UploadDir, 0755); err != nil {
		log.Printf("创建上传目录失败: %v", err)
	}

	log.Println("配置初始化完成")
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}
