src/
├── components/           # 通用组件
│   ├── FileUpload.vue   # 文件上传组件
│   ├── FileList.vue     # 文件列表组件
│   ├── FilePreview.vue  # 文件预览组件
│   └── Pagination.vue   # 分页组件
├── views/               # 页面组件
│   ├── Login.vue        # 登录页面
│   ├── Dashboard.vue    # 仪表板
│   └── FileManager.vue  # 文件管理页面
├── api/                 # API接口
│   ├── auth.ts          # 认证接口
│   └── file.ts          # 文件接口
├── store/               # 状态管理
│   ├── auth.ts          # 认证状态
│   └── file.ts          # 文件状态
├── router/              # 路由配置
│   └── index.ts
├── utils/               # 工具函数
│   ├── request.ts       # HTTP请求封装
│   └── format.ts        # 格式化工具
└── types/               # TypeScript类型定义
    ├── auth.ts
    └── file.ts